#!/usr/bin/env python3
"""
文字转语音API测试脚本
用于测试HTTP API接口是否正常工作
"""

import requests
import json
import time
import sys

def test_tts_api(text, host="localhost", port=8080):
    """测试文字转语音API"""
    url = f"http://{host}:{port}/tts"
    
    data = {
        "text": text
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"发送请求到: {url}")
        print(f"文字内容: {text}")
        
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                print("✅ 文字转语音请求发送成功！")
                return True
            else:
                print("❌ 服务器返回错误")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务器正在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_api_info(host="localhost", port=8080):
    """测试API信息接口"""
    url = f"http://{host}:{port}/"
    
    try:
        print(f"获取API信息: {url}")
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            info = response.json()
            print("✅ API信息:")
            print(json.dumps(info, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"❌ 获取API信息失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取API信息失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 文字转语音API测试 ===\n")
    
    # 解析命令行参数
    host = "localhost"
    port = 8080
    
    if len(sys.argv) > 1:
        port = int(sys.argv[1])
    if len(sys.argv) > 2:
        host = sys.argv[2]
    
    print(f"测试服务器: {host}:{port}\n")
    
    # 测试API信息
    print("1. 测试API信息接口:")
    test_api_info(host, port)
    print()
    
    # 测试文字转语音
    test_cases = [
        "你好，我是机器人",
        "今天天气真不错",
        "欢迎使用文字转语音功能",
        "这是一个测试消息"
    ]
    
    print("2. 测试文字转语音接口:")
    for i, text in enumerate(test_cases, 1):
        print(f"\n测试 {i}/{len(test_cases)}:")
        success = test_tts_api(text, host, port)
        if success:
            print("等待3秒后继续下一个测试...")
            time.sleep(3)
        else:
            print("测试失败，跳过后续测试")
            break
    
    print("\n=== 测试完成 ===")
    print(f"使用方法: python {sys.argv[0]} [端口] [主机]")
    print(f"示例: python {sys.argv[0]} 8080 localhost")

if __name__ == "__main__":
    main()
