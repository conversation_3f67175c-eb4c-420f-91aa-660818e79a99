"""
@Author: <EMAIL> <EMAIL> <EMAIL>
@Create Date: 2025.03.28
@Description: 4.9版本, 适配应用 ffplay 的 tatata_api.py

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import os
import sys
import time
import argparse
import uuid

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# from api.skill_api_w_ai_agent_magicdog import chat_with_gpt_w_ai_agent_magicdog
# from robot_agent.robot_controller_w_ai_agent_magicdog import MagicDogControllerWithAiAgent
# from robot_agent.robot_commander_magicdog import Magic<PERSON><PERSON><PERSON>mander
from voice.voice import MultiChat
from robot_agent.robot_commander import Robot<PERSON>ommander
from robot_agent.robot_config import ROBOT_CONFIG
from robot_agent.robot_controller import RobotController
from util.logger import logger
from datetime import datetime
import logging
from queue import Queue, Empty
import threading
from concurrent.futures import ThreadPoolExecutor

from api.websocket_client_thread import WebSocketClientThread
from util.audio_player import AudioPlayer
import threading
#import rospy  # 跟随
import queue
#from std_msgs.msg import String  # 跟随

import socket
from robot_agent.engineai_config import ENGINEAI_CONFIG
from robot_agent.engineai_commander import RobotCommanderEngineAI
from robot_agent.engineai_controller import RobotControllerEngineAI

import json
from robot_agent.robot_controller_w_ai_agent import RobotControllerWithAiAgent
import simpleaudio as sa
from utils.unified_audio_controller import play_audio_file
from collections import OrderedDict
import hmac
import hashlib
import binascii

from voice.voice_angle import VoiceAngle

# HTTP API 相关导入
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse

# 全局变量，用于在HTTP处理器中访问HardwareAIAgent实例
_global_hardware_ai_agent = None

class TextToSpeechHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器，用于处理文字转语音请求"""

    def do_POST(self):
        """处理POST请求"""
        try:
            # 检查路径
            if self.path not in ['/tts', '/mode', '/pending']:
                self.send_error(404, "Not Found")
                return

            # 获取请求体长度
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length == 0:
                self.send_error(400, "Empty request body")
                return

            # 读取请求体
            post_data = self.rfile.read(content_length)

            # 解析JSON数据
            try:
                data = json.loads(post_data.decode('utf-8'))
            except json.JSONDecodeError:
                self.send_error(400, "Invalid JSON")
                return

            global _global_hardware_ai_agent
            if _global_hardware_ai_agent is None:
                self.send_error(500, "Hardware AI Agent not initialized")
                return

            # 处理不同的接口
            if self.path == '/tts':
                # 文字转语音接口
                text = data.get('text', '').strip()
                if not text:
                    self.send_error(400, "Missing 'text' field")
                    return

                # 检查文字长度（API限制1000字节）
                if len(text.encode('utf-8')) > 1000:
                    self.send_error(400, "Text too long (max 1000 bytes)")
                    return

                success = _global_hardware_ai_agent.send_text_to_speech(text)

                # 如果是手动回复模式，清空待回复文本并重置模式为监听状态
                if _global_hardware_ai_agent.manual_reply_mode:
                    _global_hardware_ai_agent.clear_pending_asr_text()
                    # 重置语音模式为监听状态，等待下次唤醒
                    if hasattr(_global_hardware_ai_agent, 'mode'):
                        _global_hardware_ai_agent.mode = 'detect'
                        logger.info("🔧 手动回复完成，重置为监听状态")

                if success:
                    response = {"status": "success", "message": "Text sent for speech synthesis"}
                    self.send_response(200)
                else:
                    response = {"status": "error", "message": "Failed to send text"}
                    self.send_response(500)

            elif self.path == '/mode':
                # 模式切换接口
                mode = data.get('mode')
                if mode == 'toggle':
                    new_mode = _global_hardware_ai_agent.toggle_manual_reply_mode()
                elif mode == 'manual':
                    new_mode = _global_hardware_ai_agent.set_manual_reply_mode(True)
                elif mode == 'auto':
                    new_mode = _global_hardware_ai_agent.set_manual_reply_mode(False)
                else:
                    self.send_error(400, "Invalid mode. Use 'toggle', 'manual', or 'auto'")
                    return

                response = {
                    "status": "success",
                    "mode": "manual" if new_mode else "auto",
                    "message": f"已切换到{'手动回复' if new_mode else '自动对话'}模式"
                }
                self.send_response(200)

            elif self.path == '/pending':
                # 获取待回复文本接口
                pending_text = _global_hardware_ai_agent.get_pending_asr_text()
                response = {
                    "status": "success",
                    "pending_text": pending_text,
                    "has_pending": bool(pending_text),
                    "mode": "manual" if _global_hardware_ai_agent.manual_reply_mode else "auto"
                }
                self.send_response(200)

            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域
            self.end_headers()
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

        except Exception as e:
            logger.error(f"HTTP请求处理出错: {e}")
            self.send_error(500, f"Internal server error: {str(e)}")

    def do_GET(self):
        """处理GET请求，返回API说明或状态信息"""
        global _global_hardware_ai_agent

        if self.path == '/':
            response = {
                "service": "Text to Speech API with Manual Reply Mode",
                "endpoints": {
                    "/tts": {
                        "method": "POST",
                        "description": "发送文字转语音",
                        "format": {"text": "要合成的文字内容"},
                        "example": "curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{\"text\":\"你好，我是机器人\"}'"
                    },
                    "/mode": {
                        "method": "POST",
                        "description": "切换回复模式",
                        "format": {"mode": "toggle|manual|auto"},
                        "example": "curl -X POST http://localhost:8080/mode -H 'Content-Type: application/json' -d '{\"mode\":\"manual\"}'"
                    },
                    "/pending": {
                        "method": "POST",
                        "description": "获取待回复的语音文本",
                        "format": {},
                        "example": "curl -X POST http://localhost:8080/pending -H 'Content-Type: application/json' -d '{}'"
                    },
                    "/status": {
                        "method": "GET",
                        "description": "获取当前状态",
                        "example": "curl http://localhost:8080/status"
                    }
                }
            }
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))

        elif self.path == '/status':
            if _global_hardware_ai_agent:
                response = {
                    "status": "running",
                    "mode": "manual" if _global_hardware_ai_agent.manual_reply_mode else "auto",
                    "pending_text": _global_hardware_ai_agent.get_pending_asr_text(),
                    "has_pending": bool(_global_hardware_ai_agent.get_pending_asr_text())
                }
            else:
                response = {"status": "not_initialized"}

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
        else:
            self.send_error(404, "Not Found")

    def do_OPTIONS(self):
        """处理OPTIONS请求，支持CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def log_message(self, format, *args):
        """重写日志方法，使用项目的logger"""
        logger.info(f"HTTP {format % args}")

def is_exit_command(text: str) -> bool:
    """
    检查输入文本是否包含退出相关的关键词
    
    Args:
        text: 需要检查的输入文本
        
    Returns:
        bool: 如果包含退出关键词返回True，否则返回False
    """
    exit_keywords = {
        "关闭聊天",
        "退出",
        "别说",
        "关闭",
        "再见",
        "退下",
        "拜拜",
        "停止"
    }
    
    return any(keyword in text for keyword in exit_keywords)

import subprocess
def play_tts_voice(file_path: str):
    """
    使用统一音频控制器播放音频文件，支持嘴巴动作控制。
    :param file_path: 音频文件路径
    """
    try:
        logger.info(f"使用统一音频控制器播放: {file_path}")
        if os.path.exists(file_path):
            play_audio_file(file_path)
            logger.info(f"统一音频控制器播放完成: {file_path}")
        else:
            logger.error(f"音频文件不存在: {file_path}")
    except Exception as e:
        logger.error(f"使用统一音频控制器播放失败: {e}")
        # 回退到系统播放命令（无嘴巴动作）
        try:
            if os.name == 'nt':  # Windows
                # Windows下可以尝试其他播放方式
                logger.warning("Windows系统回退播放暂未实现")
            else:  # Linux/Unix
                logger.warning("回退到系统play命令（无嘴巴动作）")
                subprocess.run(['play', file_path], check=True)
        except Exception as e2:
            logger.error(f"回退播放也失败: {e2}")


class PrioritySlidingQueue(Queue):
    """
    带优先级的滑动队列
    """
    def put_sliding(self, item, index, sendStatus, priority=False):
        """
        将数据放入队列，priority为True时强制插入
        """
        with self.mutex:
            if priority:
                # 高优先级命令，直接清空队列
                remaining_tasks = len(self.queue)
                self.queue.clear()
                self.unfinished_tasks -= remaining_tasks  # 减去清空的任务数
            elif self.maxsize > 0 and len(self.queue) >= self.maxsize:
                # 普通命令，队列满时移除最老的
                self.queue.popleft()  # 改用 popleft() 替代 pop(0)
                self.unfinished_tasks -= 1
            
            self.queue.append((item, index, sendStatus))
            self.unfinished_tasks += 1
            self.not_empty.notify()

def check_network_connect(net_url="https://outer-voicechat.jd.com/soulmate/hello"):
    try:
        test_times = 3
        for i in range(1, test_times + 1):
            result = subprocess.run(
                ['curl', '-I', '-m', '5', net_url],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            if result.returncode == 0 and b'HTTP/' in result.stdout:
                print(f"[第{i}次] 网络连接正常，可以访问 {net_url}")
                #play_tts_voice('./asserts/tts/network_ok.mp3')  # lin bro
                return True
            else:
                print(f"[第{i}次] 警告: 无法访问 {net_url}，错误信息：{result.stderr.decode()}")
        
        play_tts_voice('./asserts/tts/network_tts.mp3')  # lin bro
        return False
    except Exception as e:
        logger.error(f"网络检查失败: {e}")
        play_tts_voice('./asserts/tts/network_tts.mp3')  # lin bro
        return False
    
def check_robot_connection(ip="*************"):
    """检查机器人连接状态"""
    try:
        import subprocess
        logger.info(f"检查网络连接到机器人 {ip}...")
        ping_result = subprocess.run(['ping', '-c', '1', '-W', '1', ip], 
                                    stdout=subprocess.PIPE, 
                                    stderr=subprocess.PIPE)
        if ping_result.returncode == 0:
            logger.info(f"网络连接正常，可以ping通机器人 {ip}")
            # play_tts_voice('./asserts/tts/network_ok.mp3')
            return True
        else:
            # play_tts_voice('./asserts/tts/network_tts.mp3')
            logger.error(f"警告: 无法ping通机器人 {ip}，请检查网络连接")
            return False
    except Exception as e:
        logger.error(f"网络检查失败: {e}")
        # play_tts_voice('./asserts/tts/network_tts.mp3')
        return False



class HardwareAIAgent(MultiChat):
    def __init__(self, det_log=1, chat_log=1, det_th=2000, chat_th=2000, commander=None, asr_engine="jd-ws-asr", kws_mode="api", use_action="dont", echo_cancel=True, with_agent=False, use_save_audio=False):
        super(HardwareAIAgent, self).__init__(
            det_log=det_log, chat_log=chat_log, det_th=det_th, chat_th=chat_th, asr_engine=asr_engine, kws_mode=kws_mode, use_action=use_action, echo_cancel=echo_cancel, use_save_audio=use_save_audio, hardware_ai_agent=self
        )


        self.use_action = use_action
        self.with_agent = with_agent


        if self.use_action == "magicdog":
            # 动态导入magicdog相关模块
            try:
                from robot_agent.robot_controller_w_ai_agent_magicdog import MagicDogControllerWithAiAgent
                self.commander = commander
                #if self.with_agent:
                self.controller = MagicDogControllerWithAiAgent(commander)
                logger.info("MagicDogControllerWithAiAgent 初始化完成")
            except ImportError as e:
                logger.error(f"无法导入MagicDog相关模块: {e}")
                self.controller = None

        elif self.use_action == "rm":
            self.controller = None
            # 创建全局命令队列用于线程通信
            # self.action_command_queue = queue.Queue()
        elif self.use_action == "dont":
            self.controller = None
        elif self.use_action == "engineai":
            self.controller = RobotControllerEngineAI(commander)
            self.controller.control_robot(0)
            logger.info("EngineAI机器人初始化姿态完成")
        else:
            raise ValueError(f"use_action参数错误: {self.use_action}")


        self.robot_up_state = 0
        self.execute_action_num = 0
        self.close_chat_flag = False
        self.wakeup_flag = False

        # 手动回复模式开关
        self.manual_reply_mode = False
        self.pending_asr_text = ""  # 存储等待手动回复的ASR文本

        self.echo_cancel = echo_cancel
        self.use_save_audio = use_save_audio


        # 小犀 (通用) 5ab96118f24a4ca086f9ae81a2a716d3
        # 晴朗 70bd8fd71b854f71a9e57d2f6a170c84
        # 小擎 94e0f6ae61274db980a03832f7ae5b95
        # 元宝 a04d378803e3429b90d86a7668b2d07f
        # 小犀（闲聊）e198cfc732ab4d238242124044f10b6f

        # 创建WebSocket连接

        self.ws_url_base = "wss://joyinside.jd.com/soulmate/voiceCall/v4"
        self.access_key = "cfa57b9ed4d740dd9128a211"
        self.access_secret = "3163a236f99c4ce7ac7d6e9c6f97f215"
        self.access_version = "V2"
        # 对话机器人ID,在云上创建对话机器人时生成
        self.bot_id = "da6fecedd53343bc8d96fe04a000c41d"
        self.user_id = "jd_4e8cf971877bc"                                    # 用户id，指企业账户体系中的用户
        self.session_id = str(uuid.uuid1(node=None, clock_seq=None))    # 会话ID，标识一次会话，关联用户和智能体对话的所有聊天记录，建议用uid+botId标识一次会话

        self.needAudio = True

        self.headers = {}
        self.message_id = str(uuid.uuid1(node=None, clock_seq=None))
        self.request_id = str(uuid.uuid1(node=None, clock_seq=None))
        self.init_websocket()



         # 创建一个主线程来管理两个执行器
        self.task_thread = threading.Thread(target=self._task_worker, daemon=True)
        self.task_thread.start()


        ## 任务队列
        self.task_executor = ThreadPoolExecutor(max_workers=2)

        self.chat_future = None
        self.control_future = None


        ## 音频播放器
        self.audio_player = AudioPlayer()
        self.audio_player.start()

        self.asr_text = ""
        self.llm_response = ""

        self.interrupt_thread = threading.Thread(target=self._interrupt_worker, daemon=True)
        self.interrupt_thread.start()

        self.start_time = time.time()

        # 已被挪走
        # if self.use_action == "lite3":
        #     try:
        #         self.command = rospy.Publisher("/track_cmd",String, queue_size=10)
        #         logger.info("跟踪模式-线程已启动，等待命令...")            
        #     except Exception as e:
        #         logger.error(f"跟踪模式-线程运行出错: {str(e)}")
        
        # if self.use_action == "rm":
        #     try:
        #         sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        #         sys.path.append('/home/<USER>/catkin_ws/src/rm_dual_arm_robot/dual_arm_control/arm_driver/src')
        #         from action_set_func import ActionSet
        #         action_set = ActionSet()
        #         self.command = rospy.Publisher("/action_command",String, queue_size=10)
        #         action_threading = threading.Thread(target=action_set.run_action_set,daemon=True )
        #         logger.info("ActionSet节点已启动，等待命令...")            
        #     except Exception as e:
        #         logger.error(f"ActionSet线程运行出错: {str(e)}")


        #self.voice_angle = VoiceAngle(controller=self.controller)

        # HTTP API服务器
        self.http_server = None
        self.http_thread = None


    def start_http_server(self, port=8080):
        """启动HTTP API服务器"""
        try:
            global _global_hardware_ai_agent
            _global_hardware_ai_agent = self

            self.http_server = HTTPServer(('0.0.0.0', port), TextToSpeechHandler)
            self.http_thread = threading.Thread(target=self.http_server.serve_forever, daemon=True)
            self.http_thread.start()
            logger.info(f"HTTP API服务器已启动，监听端口: {port}")
            logger.info(f"API地址: http://localhost:{port}/tts")
            logger.info(f"使用示例: curl -X POST http://localhost:{port}/tts -H 'Content-Type: application/json' -d '{{\"text\":\"你好，我是机器人\"}}'")
        except Exception as e:
            logger.error(f"启动HTTP服务器失败: {e}")

    def stop_http_server(self):
        """停止HTTP API服务器"""
        if self.http_server:
            self.http_server.shutdown()
            logger.info("HTTP API服务器已停止")

    def gen_sign(self, input_params, access_secret) :

        params = {}
        params["accessVersion"] = input_params["accessVersion"]
        params["accessTimestamp"] = input_params["accessTimestamp"]
        params["accessNonce"] = input_params["accessNonce"]
        params["accessKeyId"] = input_params["accessKeyId"]
        params["botId"] = input_params["botId"]

        # 将params的key转为小写并排序
        lower_params = {k.lower(): params[k] for k in params}
        sorted_params = sorted(lower_params.items(), key=lambda item: item[0])
        joint_params = '&'.join([f'{k}={str(v)}' for k, v in sorted_params])

        h = hmac.new(access_secret.encode('utf-8'), joint_params.encode('utf-8'), digestmod=hashlib.md5)
        sign = binascii.hexlify(h.digest()).decode('utf-8')
        return sign

    def init_websocket(self):

        self.asr_request_id = str(uuid.uuid4())+"_joyinside"

        params = {}
        params["accessVersion"] = self.access_version
        params["accessTimestamp"] = str(int(round(time.time() * 1000)))
        params["accessNonce"] = str(uuid.uuid4())
        params["accessKeyId"] = self.access_key
        params["botId"] = self.bot_id
        params["sessionId"] = self.session_id
        params["requestId"] = self.asr_request_id
        params["accessSign"] = self.gen_sign(params, self.access_secret)

        self.ws_url = self.ws_url_base + "?" + '&'.join([f'{k}={v}' for k, v in params.items()])
        logger.info("url: " + self.ws_url + ", request_id: " + self.asr_request_id)
        self.websocket_manager = WebSocketClientThread(self.ws_url, self.headers)
        self.websocket_manager.connect()

    def send_message(self, content, index, sendStatus):
        message = {
            "mid": str(uuid.uuid4()),
            "uid": self.user_id,
            "contentType": "AUDIO",
            "content": {
                "index": index,
                "audioBase64": content,
            }
        }
        # logger.info(f"发消息: {message}")
        self.websocket_manager.send_message(message)

    def send_text_to_speech(self, text):
        """发送文字转语音请求"""
        message = {
            "mid": str(uuid.uuid4()),
            "uid": self.user_id,
            "contentType": "EVENT",
            "content": {
                "eventType": "CLIENT_INPUT_TEXT_TO_SPEECH",
                "eventData": {"text": text}
            }
        }
        logger.info(f"发送文字转语音: {text}")
        return self.websocket_manager.send_message(message)

    def toggle_manual_reply_mode(self):
        """切换手动回复模式"""
        self.manual_reply_mode = not self.manual_reply_mode
        mode_text = "手动回复模式" if self.manual_reply_mode else "自动对话模式"
        logger.info(f"已切换到: {mode_text}")
        return self.manual_reply_mode

    def set_manual_reply_mode(self, enabled):
        """设置手动回复模式"""
        self.manual_reply_mode = enabled
        mode_text = "手动回复模式" if self.manual_reply_mode else "自动对话模式"
        logger.info(f"已设置为: {mode_text}")
        return self.manual_reply_mode

    def get_pending_asr_text(self):
        """获取等待回复的ASR文本"""
        return self.pending_asr_text

    def clear_pending_asr_text(self):
        """清空等待回复的ASR文本"""
        self.pending_asr_text = ""

    def _interrupt_worker(self):
        """中断线程，管理中断任务"""
        while True:
            #print(f"websocket_manager.task_num: {self.websocket_manager.task_num}")
            if self.websocket_manager.task_num >= 2:

                self.audio_player.interrupt()
                if self.use_action == "lite3" or self.use_action == "magicdog":
                    self.controller.stop()
                time.sleep(0.1)
                logger.info(f"任务被打断，取消执行, task_num: {self.websocket_manager.task_num}; llm_interrupt_flag: {self.websocket_manager.llm_interrupt_flag}")
            elif self.websocket_manager.llm_interrupt_flag:
                self.audio_player.interrupt()
                if self.use_action == "lite3" or self.use_action == "magicdog": 
                    self.controller.stop()
                time.sleep(0.1)
                if self.websocket_manager.task_num > 0:
                    self.websocket_manager.task_num-=1
                logger.info(f"任务被打断，取消执行, task_num: {self.websocket_manager.task_num}; llm_interrupt_flag: {self.websocket_manager.llm_interrupt_flag}")
                self.websocket_manager.llm_interrupt_flag = False
            time.sleep(0.01)
            
    def _task_worker(self):
        """主工作线程，管理控制和聊天任务"""
        while True:
            try:
                # 从队列获取任务
                
                flag, asr_text, llm_text, audio_data, audio_duration, audio_path, requestId = self.websocket_manager.receive_message()

                # 处理流式音频片段
                if flag == 2:  # 音频片段
                    if audio_data:
                        self.audio_player.add_to_queue(audio_data, 0)  # 立即播放音频片段
                    continue

                # 处理完成信号
                if flag == 3:  # 完成信号
                    logger.info("音频流式播放完成")
                    continue

                if flag is None:
                    if self.close_chat_flag:
                        self.websocket_manager.receive_queue.queue.clear()
                        self.close_chat_flag = False
                        self.websocket_manager.task_num -=1
                        continue

                    
                    if requestId+"-response_time" in self.websocket_manager.dict_message_id:
                        requestId_response_time = self.websocket_manager.dict_message_id[requestId+"-response_time"]
                    else:
                        requestId_response_time = 0
                    if requestId+"-asr_text" in self.websocket_manager.dict_message_id:
                        requestId_asr_text = self.websocket_manager.dict_message_id[requestId+"-asr_text"]
                    else:
                        requestId_asr_text = ""

                    logger.info(f"session_id: {self.session_id}; requestId: {requestId}; asr: {requestId_asr_text}; 响应时间: {requestId_response_time}; JD机器人回复: {self.llm_response}")

                    # 优化：不等待动作控制完成，让音频和动作并行执行
                    logger.info("等待音频播放完成")
                    self.audio_player.wait_for_completion()
                    
                    # 音频播放完成后再等待动作控制完成（如果还在执行）
                    if self.control_future is not None:
                        logger.info("等待控制完成")
                        self.control_future.result()

                    if self.echo_cancel == False:
                        self.record_flag = True

                    if self.websocket_manager.task_num > 0:
                        self.websocket_manager.task_num -=1
                    self.llm_response = ""
                    self.asr_text = ""
                    logger.info("任务完成，继续")
                    continue
                else:
                    if flag == 0:
                        self.asr_text = asr_text

                    if not self.close_chat_flag and self.websocket_manager.task_num <= 1:
                        
                        if flag == 0:
                            #self.voice_angle.execute_skill_locator()
                            if is_exit_command(self.asr_text):
                                self.close_chat_flag = True

                                if self.use_action == "lite3":
                                    # 停止跟踪功能
                                    #self.tracking_helper.stop_tracking()  # tracker
                                    self.controller.control_robot(9)
                                self.mode = 'wait'
                                #print(self.mode)
                                temp_file = './asserts/ding.wav'
                                play_audio(temp_file)
                                #subprocess.run(['ffplay', '-nodisp', '-autoexit', '-i', temp_file], stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
                                # self.chat_id = str(uuid.uuid1(node=None, clock_seq=None))
                                # self.init_websocket()
                                continue

                            # 检查是否为手动回复模式
                            if self.manual_reply_mode:
                                # 手动回复模式：只存储ASR文本，不发送给大模型
                                self.pending_asr_text = self.asr_text
                                logger.info(f"🔧 手动回复模式 - 收到语音: {self.asr_text}")
                                logger.info("等待手动输入回复内容...")

                                # 播放提示音表示收到语音
                                temp_file = './asserts/ding.wav'
                                play_audio(temp_file)

                                # 减少任务计数，因为不会有后续的TTS响应
                                if self.websocket_manager.task_num > 0:
                                    self.websocket_manager.task_num -= 1
                                continue

                            if self.use_action == "lite3" or self.use_action == "engineai" or self.use_action == "magicdog":
                                logger.info(f"准备请求动作AGENT：{asr_text}")
                                self.control_future = self.task_executor.submit(self.control_robot, asr_text)
                        elif flag == 1:
                            logger.info("存入音频")
                            self.audio_player.start_time = self.websocket_manager.receive_tts_time
                            self.audio_player.add_to_queue(audio_data, audio_duration)
                            self.llm_response += llm_text

                        elif flag == 2:
                            logger.info("播放音频")
                            play_audio('./asserts/' + audio_path)
                time.sleep(0.01)
            except Empty:
                time.sleep(0.01)
                continue
            except Exception as e:
                import traceback
                logger.error(f"执行任务出错: {traceback.format_exc()}")

    
    
    @property
    def current_mode(self):
        return self.mode
    
    @property
    def current_wakeup(self):
        return self.wakeup_flag

            
    def control_robot(self, input_string):
        if self.use_action == "lite3" and self.with_agent:
            return


        # elif self.use_action == "magicdog":
        elif self.use_action == "magicdog":
            try:
                from api.skill_api_w_ai_agent_magicdog import chat_with_gpt_w_ai_agent_magicdog
                from robot_agent.robot_controller_w_ai_agent_magicdog import MagicDogControllerWithAiAgent

                current_state = self.controller.commander.current_state
                # current_state = self.robot_state
                json_str = chat_with_gpt_w_ai_agent_magicdog(input_string, current_state)   # {"actions": ["前进"], "is_valid": True}
                robot_command = json.loads(json_str)
                logger.info(f"Agent识别结果：{robot_command}")

                # 检查是否有效并且包含动作列表
                #robot_command.get("is_valid", False) and
                if  "actions" in robot_command:
                    if isinstance(self.controller, MagicDogControllerWithAiAgent):
                        self.controller.process_action_list(robot_command["actions"])
                    else:
                        logger.error("控制器类型不匹配，无法处理动作列表")
            except ImportError as e:
                logger.error(f"无法导入MagicDog相关模块: {e}")



    def stop(self):
        # 停止跟踪程序
        #self.tracking_helper.stop_tracking()
        try:
            # 关闭线程池
            super().stop()
            if self.use_action == "lite3":
                self.commander.on_closing()
            
            
            # 关闭线程池
            # self.chat_executor.shutdown(wait=True)

            if self.use_action == "lite3":
                self.task_executor.shutdown(wait=True)
            
            # 等待主线程结束
            self.task_thread.join()
            self.interrupt_thread.join()

            self.audio_player.stop()
            self.websocket_manager.stop()
            self.voice_angle.stop()
        
            ##self.executor.shutdown(wait=True)
        except Exception as e:
            logger.error(f"停止时发生错误: {e}")



def play_audio(tempfile):
    """使用 audio_action_controller 播放音频并控制动作"""
    logger.info(f"准备播放音频文件: {tempfile}")

    # 检查文件是否存在
    if not os.path.exists(tempfile):
        logger.error(f"音频文件不存在: {tempfile}")
        return

    try:
        logger.info(f"使用统一音频控制器播放: {tempfile}")
        play_audio_file(tempfile)
        logger.info(f"统一音频控制器播放完成: {tempfile}")
    except Exception as e:
        logger.error(f"使用统一音频控制器播放失败，回退到 simpleaudio: {e}")
        # 回退到原来的 simpleaudio 方式
        try:
            wave_obj = sa.WaveObject.from_wave_file(tempfile)
            play_obj = wave_obj.play()
            play_obj.wait_done()
            logger.info(f"simpleaudio 播放完成: {tempfile}")
        except Exception as e2:
            logger.error(f"simpleaudio 播放也失败: {e2}")
 

if __name__ == '__main__':
    #play_tts_voice('./asserts/tts/wait_log.mp3')  # lin bro
    parser = argparse.ArgumentParser(description="ASR LLM Params")
    parser.add_argument('--det_log', type=int, default=0, choices=[0, 1, 2],
                        help="唤醒词是否打印日志，0为不打印，1为打印，2为详细打印，默认值为0")
    parser.add_argument('--chat_log', type=int, default=2, choices=[0, 1, 2],
                        help="对话是否打印日志，0为不打印，1为打印，2为详细打印，默认值为1")
    parser.add_argument('--sound_th', type=int, default=5000,
                        help="声音检测的阈值，默认值为5000")
    parser.add_argument('--asr_engine', type=str, default="tencent-asr",
                        help="语音识别引擎，默认使用京东语音识别 jd-ws-asr") #tencent-asr
    parser.add_argument('--kws', type=str, default="api", choices=["api", "local", "local_streaming"],
                        help="唤醒词检测模式: api使用云端检测, local使用本地KWS检测, local_streaming使用本地流式KWS检测")
    
    # parser.add_argument('--use_action', type=bool, default=False,
    #                     help="是否使用动作")
    parser.add_argument('--use_action', type=str, default="dont", choices=["lite3", "dont", "rm", "engineai","magicdog"],
                        help="是否使用动作")


    parser.add_argument('--echo_cancel', type=bool, default=False,
                        help="是否存在回声消除")
    
    parser.add_argument('--with_agent', type=bool, default=False, help="是否使用Agent")

    parser.add_argument('--use_save_audio', type=bool, default=False, help="是否保存音频")

    parser.add_argument('--http_port', type=int, default=8080, help="HTTP API服务器端口，默认8080")

    args = parser.parse_args()

    check_network_connect()
    
    if args.use_action == "lite3": 
        # 从配置文件获取机器人IP
        robot_ip = ROBOT_CONFIG.get("ctl_ip", "*************")
        # 检查机器人连接
        check_result = check_robot_connection(robot_ip)
        while not check_result:
            check_result = check_robot_connection(robot_ip)
            time.sleep(1)
        # 初始化机器人控制器
        logger.info("初始化机器人控制器...")
        commander = RobotCommander()
        logger.info("初始化机器人控制器...1")

        result_heartbeat = commander.run('心跳')
        logger.info("初始化机器人控制器...2")

        while not check_result:
            logger.error("机器人控心跳连接未建立.")
            result_heartbeat = commander.run('心跳')
            time.sleep(1)
    if args.use_action == "magicdog":
        # 初始化机器人控制器
        try:
            from robot_agent.robot_commander_magicdog import MagicdogCommander
            logger.info("初始化机器人控制器...")
            commander = MagicdogCommander()
            commander.initialize()
            commander.start()
            commander.raise_up()
            logger.info("初始化机器人控制器完成")
        except ImportError as e:
            logger.error(f"无法导入MagicDog SDK: {e}")
            logger.error("请安装py_magicdog_sdk或使用其他use_action选项")
            sys.exit(1)
    elif args.use_action == "rm":
        # action_threading = threading.Thread(target=run_action_set,daemon=True )
        # action_threading.start()
        commander = None
    elif args.use_action == "dont":
        commander = None
    elif args.use_action == "engineai":
        # 从配置文件获取机器人IP和端口
        engineai_ip = ENGINEAI_CONFIG.get("engineai_tcp_ip", "*************")
        engineai_port = ENGINEAI_CONFIG.get("engineai_tcp_port", 30000)
        
        # 测试连接
        logger.info(f"尝试连接EngineAI机器人 {engineai_ip}:{engineai_port}...")
        
        # 初始化机器人通信管理器
        commander = RobotCommanderEngineAI(engineai_ip, engineai_port)
        
        # 检查连接
        check_result = commander.check_connection()
        
        # 连接失败时重试
        retry_count = 0
        while not check_result and retry_count < 3:
            logger.error(f"连接EngineAI机器人失败，正在重试...")
            check_result = commander.check_connection()
            retry_count += 1
            time.sleep(1)
        
        if check_result:
            logger.info("成功连接到EngineAI机器人")
        else:
            logger.error(f"无法连接到EngineAI机器人，程序将继续但机器人控制功能可能不可用")
    else:
        raise ValueError(f"use_action参数错误: {args.use_action}")
    
    logger.info(f"use_action: {args.use_action}")
    logger.info("\n[启动HardwareAIAgent交互程序]\n")
    hardware_ai_agent = HardwareAIAgent(det_log=args.det_log,
                                        chat_log=args.chat_log,
                                        det_th=args.sound_th,
                                        chat_th=args.sound_th,
                                        commander=commander,
                                        asr_engine=args.asr_engine,
                                        kws_mode=args.kws,
                                        use_action=args.use_action,
                                        echo_cancel=args.echo_cancel,
                                        with_agent=args.with_agent,
                                        use_save_audio=args.use_save_audio)



    try:
        hardware_ai_agent.init_wakeup()
        time.sleep(1)
        #import subprocess
        temp_file = './asserts/ding.wav'

        play_audio(temp_file)

        play_tts_voice("asserts/tts/dog_ok.mp3")  # lin bro

        # 启动HTTP API服务器
        hardware_ai_agent.start_http_server(port=args.http_port)

        hardware_ai_agent.run()




    except KeyboardInterrupt:
        hardware_ai_agent.stop()
        hardware_ai_agent.stop_http_server()

