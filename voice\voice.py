import os
import sys
import json
import io
import re
import copy
import urllib
import time
from pathlib import Path
import argparse
import threading
import pyaudio
#import requests
import wave
import numpy as np
import random
import functools
#from pypinyin import pinyin, Style
#import subprocess

#from asr.robot import ASR
sys.path.append((Path(__file__).resolve().parent/"src").__str__())
sys.path.append(Path(__file__).resolve().parent.__str__())
from datetime import datetime
from .constants import no_alsa_error
import collections
import queue
#import webrtcvad
from collections import deque
from util.logger import logger
import base64


import simpleaudio as sa
from utils.unified_audio_controller import play_audio_file, interrupt_audio


class RingBuffer(object):
    """Ring buffer to hold audio from PortAudio"""

    def __init__(self, size=4096):
        self._buf = collections.deque(maxlen=size)

    def extend(self, data):
        """Adds data to the end of buffer"""
        self._buf.extend(data)

    def get(self):
        """Retrieves data from the beginning of buffer and clears it"""
        tmp = bytes(bytearray(self._buf))
        self._buf.clear()
        return tmp

    
def play_audio_wakeup():
    """
    播放音频数据
    
    参数:
        audio_data: 解码后的音频二进制数据
    """
    try:
        
        # import simpleaudio as sa
        # 先将音频数据写入临时文件
        # temp_file = "temp_output.wav"
        # with open(temp_file, 'wb') as f:
        #     f.write(audio_data)
        
        # 使用 aplay 播放音频
        try:

            tempfile_zaine = "asserts/zaine.wav"
            tempfile_shenmeshi = "asserts/shenmeshi.wav"

            if random.random() < 0.5:
                tempfile = tempfile_zaine
                #play_tts_voice('../asserts/zaine.mp3')  # lin bro
            else:
                #play_tts_voice('./asserts/shenmeshi.mp3')  # lin bro
                tempfile = tempfile_shenmeshi


             # wave_obj = sa.WaveObject.from_wave_file('/home/<USER>/Possessed_AI/asserts/dog_bark.wav')
            # play_obj = wave_obj.play()
            # play_obj.wait_done()  # 等待播放完成

            """ffplay"""
            # subprocess.run(['ffplay', '-nodisp', '-autoexit', '-i', tempfile], stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)

            """使用 audio_action_controller 播放音频并控制动作"""
            logger.info(f"准备播放唤醒音频: {tempfile}")

            # 检查文件是否存在
            if not os.path.exists(tempfile):
                logger.error(f"唤醒音频文件不存在: {tempfile}")
                return

            try:
                logger.info(f"使用统一音频控制器播放唤醒音频: {tempfile}")
                play_audio_file(tempfile)
                logger.info(f"唤醒音频播放完成: {tempfile}")
            except Exception as e:
                logger.error(f"使用统一音频控制器播放失败，回退到 simpleaudio: {e}")
                # 回退到原来的 simpleaudio 方式
                try:
                    wave_obj = sa.WaveObject.from_wave_file(tempfile)
                    play_obj = wave_obj.play()
                    play_obj.wait_done()
                    logger.info(f"simpleaudio 播放唤醒音频完成: {tempfile}")
                except Exception as e2:
                    logger.error(f"simpleaudio 播放唤醒音频也失败: {e2}")

            #subprocess.run(['aplay', '/home/<USER>/Possessed_AI/asserts/dog_bark.wav'],stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
            #print("使用ffplay播放成功")
            return
        except FileNotFoundError:
            # print("ffplay不可用，尝试其他播放器")
            logger.error(f"唤醒音频文件未找到: {tempfile}")
        
    except Exception as e:
        print(f"播放音频时出错: {str(e)}")


import subprocess
def play_tts_voice(file_path: str):
    """
    使用统一音频控制器播放音频文件，支持嘴巴动作控制。
    :param file_path: 音频文件路径
    """
    try:
        from utils.unified_audio_controller import play_audio_file
        logger.info(f"使用统一音频控制器播放: {file_path}")
        if os.path.exists(file_path):
            play_audio_file(file_path)
            logger.info(f"统一音频控制器播放完成: {file_path}")
        else:
            logger.error(f"音频文件不存在: {file_path}")
    except Exception as e:
        logger.error(f"使用统一音频控制器播放失败: {e}")
        # 回退到系统播放命令（无嘴巴动作）
        try:
            logger.warning("回退到系统play命令（无嘴巴动作）")
            subprocess.run(['play', file_path], check=True)
        except Exception as e2:
            logger.error(f"回退播放也失败: {e2}")

class MultiChat(object):
    def __init__(self, det_log=1, chat_log=1, det_th=2000, chat_th=2000, asr_engine="jd-ws-asr", kws_mode="api", use_action="dont", echo_cancel=True, use_save_audio=False, hardware_ai_agent=None):
        super(MultiChat, self).__init__()

        # 添加保存音频的选项
        self.use_save_audio = use_save_audio

        # 引用主控制器，用于检查手动回复模式
        self.hardware_ai_agent = hardware_ai_agent

        # ---------------------------- #
        self.CHUNK = 0.06  # ms
        self.FORMAT = pyaudio.paInt16  # 采样位数
        self.CHANNELS = 1  # 声道数
        self.RATE = 16000  # 采样率，每秒采集的样例数
        self._running = True
        self.lock = threading.Lock()  # 创建独立的锁
        self.END_COUNTER = 1
        self.test_count = 0

        self.device_info = None
        # resample
        self.audio_resample = []
        self.DEVICE_CHANNELS = 1
        self.DEVICE_RATE = 16000
        self.ratio = self.RATE / float(self.DEVICE_RATE)

        # KWS模式设置
        self.kws_mode = kws_mode
        self.kws_detector = None

        self.use_action = use_action
        self.echo_cancel = echo_cancel

        # 创建两个缓冲区
        # self.ring_buffer = RingBuffer(
        #     self.CHANNELS * self.RATE * 5
        # )

        self.ring_buffer = queue.Queue(maxsize=10000)

        self.detector_ring_buffer = RingBuffer(
            self.CHANNELS * self.RATE * 5
        )

        self.mode = 'wait'
        self.log = chat_log

            
        self.silent_th = chat_th
        self.silent_status = True
        self.silent_temp = sys.maxsize
        self.silent_count = 0
        self.silent_count_threshold = 8

        self.save_record_file = "asserts/save_listen_file.wav"
        self.messages = []

        
        ###
        ###
        # 创建共享的音频设备
        self.audio = None
        self.stream = None

        self.robot_up_state = 0
        self.commander = None


        self.wakeup_flag = False



        self.websocket_send_index = 0

        self.record_flag = True

    def save_audio(self, audio, frames, save_file, channels=1, rate=16000):
        # 保存为wav文件
        wf = wave.open(save_file, 'wb')
        wf.setnchannels(channels)  # 设置声道数
        wf.setsampwidth(audio.get_sample_size(self.FORMAT))  # 设置采样宽度
        wf.setframerate(rate)  # 设置采样率
        wf.writeframes(b''.join(frames))  # 写入音频数据
        wf.close()

    def start_streaming(self):
        #self.ring_buffer.get()
        logger.info("[start recording]...")
        self.stream.start_stream()

    def end_streaming(self):
        logger.info("[end recording]...")
        self.stream.stop_stream()

    def stream_downsample(self, chunk, ratio=3):
        new_length = int(len(chunk) * ratio)
        resampled = np.interp(
            np.linspace(0, len(chunk) - 1, new_length),
            np.arange(len(chunk)),
            chunk
        )
        return resampled

    def audio_callback(self, in_data, frame_count, time_info, status):
            # 向两个缓冲区发送数据
            ##self.ring_buffer.extend(in_data)
            #self.detector_ring_buffer.extend(in_data)
        if in_data is None or len(in_data) == 0:
            return chr(0), pyaudio.paContinue

        # # 如果是多通道，转换为单通道
        # if self.device_info['maxInputChannels'] > 1:
        #     in_data = audioop.tomono(in_data, 2, 1, 1)  # 2 表示 16-bit 数据

        # # 如果采样率不是 16000，重新采样到 16000
        # if self.device_info['defaultSampleRate'] != self.RATE:
        #     in_data, self.state = audioop.ratecv(in_data, 2, 1, self.sample_rate, self.RATE, self.state)

        audio_data = np.frombuffer(in_data, dtype=np.int16)
        if self.DEVICE_CHANNELS > self.CHANNELS:
            first_channel = audio_data[0::self.DEVICE_CHANNELS]
            audio_data = first_channel

        if self.DEVICE_RATE!=self.RATE:
            audio_data = audio_data.astype(np.float32) / 32768.0
            extended_chunk = np.concatenate([self.buffer, audio_data]) 

            resampled_data = self.stream_downsample(extended_chunk, self.ratio)
            overlap_resampled = int(self.overlap_size * self.ratio)
            self.buffer = audio_data[-self.overlap_size:]
            resampled_data = resampled_data[overlap_resampled:]
            audio_data = (resampled_data * 32768.0).astype(np.int16)

        audio_data = audio_data.tobytes()
        #self.audio_resample.append(audio_data)
        #self.test_count += 1
        #if self.test_count % 500 == 0:
        #    logger.info("保存音频文件")
        #    wf = wave.open("test_resample.wav", 'wb')
        #    wf.setnchannels(1)  # 设置声道数
        #    wf.setsampwidth(self.audio.get_sample_size(self.FORMAT))  # 设置采样宽度
        #    wf.setframerate(16000)  # 设置采样率
        #    wf.writeframes(b''.join(self.audio_resample))  # 写入音频数据
        #    wf.close()

        if self.echo_cancel:
            self.ring_buffer.put(audio_data)
        else:
            if self.websocket_manager.code != 200:
                self.record_flag  = True
            if (not self.audio_player.pygame.mixer.music.get_busy()) and self.audio_player.audio_queue.empty() and self.record_flag:
                self.ring_buffer.put(audio_data)
        
        # 将数据也发送给KWS检测器
        self.kws_detector.accept_audio(audio_data)
        
        play_data = chr(0) * len(audio_data)
        return play_data, pyaudio.paContinue

    def init_wakeup(self):
        # 首先初始化音频设备
        #
        self.audio = pyaudio.PyAudio()
        if self.kws_mode == "local_streaming":
            # 本地流式KWS模式
            try:
                from .kws_wrapper import KWSStreamDetector
                
                # 创建流式KWS检测器
                self.kws_detector = KWSStreamDetector()
                self.kws_detector.set_detection_callback(self.detect_callback)
                
                # 重新设置音频流回调
                self._setup_audio_stream(self.audio_callback)
                
                # 启动KWS检测器
                self.kws_detector.start()
                logger.info("本地流式KWS检测器启动成功")
            
            except Exception as e:
                logger.error(f"初始化本地流式KWS失败: {e}")
    
    def _setup_audio_stream(self, audio_callback):
        """重新设置音频流回调"""
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
        
        input_device_index = self._select_input_device_auto()
        #input_device_index = 25  # 0
        logger.info(f"使用音频设备: {input_device_index}")
        logger.info(f"channels: {self.device_info['maxInputChannels']} {type(self.device_info['maxInputChannels'])}")
        logger.info(f"rate: {self.device_info['defaultSampleRate']} {type(self.device_info['defaultSampleRate'])}")
        self.DEVICE_CHANNELS = self.device_info['maxInputChannels']
        self.DEVICE_RATE = self.device_info['defaultSampleRate']
        self.ratio = self.RATE / float(self.DEVICE_RATE)

        self.overlap_size = int(self.CHUNK * self.DEVICE_RATE * 0.5)
        self.buffer = np.zeros((self.overlap_size, ), dtype=np.float32)

        self.stream = self.audio.open(
            format=self.FORMAT,
            channels=self.DEVICE_CHANNELS,
            rate=int(self.DEVICE_RATE),
            input=True,
            output=False,
            frames_per_buffer=int(self.CHUNK * self.DEVICE_RATE),
            start=True,
            stream_callback=audio_callback,
            input_device_index=input_device_index,
        )

    def print_device_info(self):
        print(f"Using device: {self.device_info['name']}")
        sample_rate = self.device_info['defaultSampleRate']
        print(f"  Sample Rate: {sample_rate}")
        channels = self.device_info['maxInputChannels']
        print(f"  Channels: {channels}")
        print(f"  Bit: {self.audio.get_sample_size(self.FORMAT)}")
        # print(f"  Sample Size: {self.audio.get_sample_size(self.FORMAT)}")
        # print(f"  Latency: {self.device_info['defaultLowInputLatency']}")


    def _select_input_device_auto(self) -> int:
        """持续检测直到找到目标设备"""
        #target_device_name = "4-mic Microphone: USB Audio"
        #机器人自身的麦克风 pulse
        # target_device_name = ["USB Device 0x46d:0x825: Audio (hw:3,0)"]#树莓派
        target_device_name = ["麦克风阵列"]#笔记本windows
        # target_device_name = ["Wireless Microphone RX", "XFM-DP-V0.0.18: USB Audio", "DOV: USB Audio", "MAXHUB_BM20_Speaker", "XFM-DP-V0.0.18", "mic","CP900: USB Audio", "spdif","dsnooper","speaker","XFM-DP-V0.0.18","麦克风阵列"]
        
        # 获取所有音频设备
        while True:  # 持续循环直到找到设备
            print("\n正在搜索音频设备...")
            input_devices = []
            selected_index = None
            selected_device_name = None
            
            # 遍历所有音频设备
            for i in range(self.audio.get_device_count()):
                device_info = self.audio.get_device_info_by_index(i)
                if device_info.get('maxInputChannels') > 0:  # 只显示输入设备
                    input_devices.append((i, device_info))
                    device_name = device_info.get('name')
                    print(f"{len(input_devices)-1}) {device_name}")
                    
                    # 自动匹配目标设备
                    for target in target_device_name:
                        # if target in device_name:
                        if target.lower() in device_name.lower():
                            selected_index = i
                            selected_device_name = device_name
                            self.device_info = device_info
                            self.print_device_info()
                            break  # 找到目标设备后立即跳出循环
                    if selected_index is not None:
                        break
            
            if not input_devices:
                print("未找到任何输入设备，3秒后重试...")
                time.sleep(3)
                continue
            
            if selected_index is not None:
                print(f"\n已找到目标设备: {selected_device_name}")
                return selected_index
            else:
                play_tts_voice('/home/<USER>/network_func/test_tts/error_audio.mp3')
            
            # 如果没找到目标设备，等待后重试
            print(f"\n未找到目标设备: {target_device_name}，3秒后重试...")
            time.sleep(3)

    def stop(self):
        """停止所有线程"""
        self._running = False
        
        # 调用原有的清理代码
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
        if self.audio:
            self.audio.terminate()

        # 如果使用的是本地KWS，停止KWS检测器
        if self.kws_mode == "local" or self.kws_mode == "local_streaming" and self.kws_detector:
            self.kws_detector.stop()
            logger.info(f"已停止{self.kws_mode}检测器")

    def detect_callback(self):

        with self.lock:
            logger.info("[wakeup] 检测到唤醒词")
            

            self.end_streaming()
            #清空音频缓冲区
            self.ring_buffer.queue.clear()
            self.websocket_manager.wakeup_flag = True

            self.websocket_manager.wakeup_num +=1
            
            # 关键修复：清空WebSocket接收队列，避免旧回答继续播放
            logger.info("[wakeup] 清空WebSocket接收队列")
            try:
                while not self.websocket_manager.receive_queue.empty():
                    self.websocket_manager.receive_queue.get_nowait()
            except:
                pass
            
            # 重置WebSocket相关状态
            self.websocket_manager.llm_interrupt_flag = True
            self.websocket_manager.full_audio_base64 = []
            self.websocket_manager.llm_text = ""
            # 重置任务计数，避免任务堆积
            self.websocket_manager.task_num = 0
            
            self.audio_player.interrupt()
            # 打断 audio_action_controller 的音频播放
            interrupt_audio()
            if self.use_action == "lite3":
                self.controller.stop()
                self.controller.turn_off_follow()

            #self.voice_angle.execute_skill_locator()
            
            play_audio_wakeup()
            # time.sleep(0.1)
            self.wakeup_flag = True

            # 检查是否为手动回复模式
            is_manual_mode = False
            if self.hardware_ai_agent and hasattr(self.hardware_ai_agent, 'manual_reply_mode'):
                is_manual_mode = self.hardware_ai_agent.manual_reply_mode

            if is_manual_mode:
                # 手动回复模式：不启动录音，直接等待手动输入
                logger.info("🔧 手动回复模式 - 等待手动输入回复内容")
                self.mode = 'wait'  # 设置为等待模式，不录音
            else:
                # 自动对话模式：正常启动录音
                if self.echo_cancel == False:
                    self.record_flag = True

                self.start_streaming()
                self.mode = 'hot'
                #self.init_websocket()
                self.websocket_manager.connect()

            if self.use_action == "lite3":
                self.controller.control_robot(7)
            if self.use_action == "magicdog":
                self.controller.control_robot(7)

    def run(self):
        try:
            # 如果启用了音频保存，创建recordings目录
            recordings_dir = None
            control_file = None
            
            if self.use_save_audio:
                # 创建recordings目录
                recordings_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "recordings")
                os.makedirs(recordings_dir, exist_ok=True)
                
                # 控制文件路径
                control_file = os.path.join(recordings_dir, "record_control.txt")
                logger.info(f"音频将保存至: {recordings_dir}")
                logger.info(f"创建文件 {control_file} 启动录音，删除该文件停止录音")  # touch recordings/record_control.txt , rm recordings/record_control.txt

            while self._running is True:
                if self.mode == 'hot':
                    self.mode = 'detect'
                    logger.info("[run] 持续监听状态...")
                elif self.mode == 'wait':
                    # 手动回复模式：等待状态，不录音
                    time.sleep(0.1)
                    continue
                if self.mode == 'detect' or self.mode == 'record':
                    # 检查是否为手动回复模式
                    is_manual_mode = False
                    if self.hardware_ai_agent and hasattr(self.hardware_ai_agent, 'manual_reply_mode'):
                        is_manual_mode = self.hardware_ai_agent.manual_reply_mode

                    if is_manual_mode:
                        # 手动回复模式：不录音，不上传，只等待
                        logger.debug("🔧 手动回复模式 - 跳过录音和上传")
                        time.sleep(0.1)
                        continue

                    # 自动对话模式：正常录音和上传
                    data = self.ring_buffer.get()
                    if len(data) == 0:
                        time.sleep(0.03)
                        continue

                    # 通过检查控制文件是否存在来决定是否保存音频
                    recording_enabled = False
                    if self.use_save_audio and recordings_dir and control_file:
                        recording_enabled = os.path.exists(control_file)

                    if self.use_save_audio and recording_enabled:
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                        audio_filename = f"recording_{timestamp}.wav"
                        audio_path = os.path.join(recordings_dir, audio_filename)

                        # 保存为WAV文件
                        with wave.open(audio_path, 'wb') as wf:
                            wf.setnchannels(self.CHANNELS)
                            wf.setsampwidth(self.audio.get_sample_size(self.FORMAT))
                            wf.setframerate(16000)  # self.RATE
                            wf.writeframes(data)

                    self.websocket_send_index += 1
                    base64_data = base64.b64encode(data).decode('utf-8')
                    self.send_message(base64_data,self.websocket_send_index,0)
                    #self.mode = 'detect'
                        
                else:
                    #print(self.model)
                    self.ring_buffer.queue.clear()
                    time.sleep(0.01)
        except KeyboardInterrupt:
            self.stop()
