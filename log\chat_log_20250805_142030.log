2025-08-05 14:20:31.938 - chat_with_robot - chat_with_robot.py - <module> - line 800 - INFO - use_action: dont
2025-08-05 14:20:31.939 - chat_with_robot - chat_with_robot.py - <module> - line 801 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 14:20:31.945 - chat_with_robot - chat_with_robot.py - init_websocket - line 451 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754374831946&accessNonce=04376e67-f66e-4910-bd3f-f615a38dfbcc&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=4a1d557e-71c4-11f0-9269-dc4546c07870&requestId=0ee431e5-b941-4267-aa9a-7b0c65c61afd_joyinside&accessSign=7540ab80feaaf9ceeb2b2bfa16942073, request_id: 0ee431e5-b941-4267-aa9a-7b0c65c61afd_joyinside
2025-08-05 14:20:31.946 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 14:20:31.946 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 14:20:32.530 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 14:20:32.635 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
