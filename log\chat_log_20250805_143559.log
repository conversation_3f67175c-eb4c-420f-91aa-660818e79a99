2025-08-05 14:36:00.315 - chat_with_robot - chat_with_robot.py - <module> - line 921 - INFO - use_action: dont
2025-08-05 14:36:00.315 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 14:36:00.322 - chat_with_robot - chat_with_robot.py - init_websocket - line 533 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754375760322&accessNonce=7d7d2ad0-bd37-45e8-bad2-54a86ddc16a1&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=73787217-71c6-11f0-9154-dc4546c07870&requestId=3c1eab3d-7147-4bdf-905a-816fe92525b2_joyinside&accessSign=75b444e9d43d43f77bd0d38e432142b9, request_id: 3c1eab3d-7147-4bdf-905a-816fe92525b2_joyinside
2025-08-05 14:36:00.324 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 14:36:00.324 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 14:36:00.852 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 14:36:00.942 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
